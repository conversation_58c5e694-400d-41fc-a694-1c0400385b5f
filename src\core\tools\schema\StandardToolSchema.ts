import { ToolName } from "../../../shared/tools"

/**
 * Standard tool definition schema that can be rendered to different formats
 */
export interface StandardToolDefinition {
	name: ToolName
	description: string
	parameters: StandardToolParameter[]
	examples?: StandardToolExample[]
	category?: string
	deprecated?: boolean
}

export interface StandardToolParameter {
	name: string
	type: "string" | "number" | "boolean" | "array" | "object"
	description: string
	required: boolean
	default?: any
	enum?: string[]
	items?: StandardToolParameter // For array types
	properties?: StandardToolParameter[] // For object types
}

export interface StandardToolExample {
	description: string
	parameters: Record<string, any>
	expectedResult?: string
}

/**
 * Registry of all standard tool definitions
 */
export class StandardToolRegistry {
	private static instance: StandardToolRegistry
	private tools = new Map<ToolName, StandardToolDefinition>()

	public static getInstance(): StandardToolRegistry {
		if (!StandardToolRegistry.instance) {
			StandardToolRegistry.instance = new StandardToolRegistry()
			StandardToolRegistry.instance.initializeTools()
		}
		return StandardToolRegistry.instance
	}

	private initializeTools(): void {
		// Define all tools in standard format
		this.registerTool({
			name: "read_file",
			description: "Read the contents of a file",
			category: "file_operations",
			parameters: [
				{
					name: "path",
					type: "string",
					description: "The path to the file to read",
					required: true
				},
				{
					name: "start_line",
					type: "number",
					description: "The starting line number (1-based)",
					required: false
				},
				{
					name: "end_line",
					type: "number",
					description: "The ending line number (1-based)",
					required: false
				}
			],
			examples: [
				{
					description: "Read entire file",
					parameters: { path: "src/main.ts" }
				},
				{
					description: "Read specific lines",
					parameters: { path: "src/main.ts", start_line: 10, end_line: 20 }
				}
			]
		})

		this.registerTool({
			name: "write_to_file",
			description: "Write content to a file",
			category: "file_operations",
			parameters: [
				{
					name: "path",
					type: "string",
					description: "The path to the file to write",
					required: true
				},
				{
					name: "content",
					type: "string",
					description: "The content to write to the file",
					required: true
				}
			],
			examples: [
				{
					description: "Create a new file",
					parameters: { 
						path: "src/new-file.ts", 
						content: "export const greeting = 'Hello World';" 
					}
				}
			]
		})

		this.registerTool({
			name: "execute_command",
			description: "Execute a shell command",
			category: "system",
			parameters: [
				{
					name: "command",
					type: "string",
					description: "The command to execute",
					required: true
				},
				{
					name: "cwd",
					type: "string",
					description: "The working directory for the command",
					required: false
				}
			],
			examples: [
				{
					description: "List files",
					parameters: { command: "ls -la" }
				},
				{
					description: "Run command in specific directory",
					parameters: { command: "npm install", cwd: "/path/to/project" }
				}
			]
		})

		this.registerTool({
			name: "apply_diff",
			description: "Apply a diff patch to a file",
			category: "file_operations",
			parameters: [
				{
					name: "path",
					type: "string",
					description: "The path to the file to modify",
					required: true
				},
				{
					name: "diff",
					type: "string",
					description: "The diff content to apply",
					required: true
				}
			],
			examples: [
				{
					description: "Apply a simple diff",
					parameters: { 
						path: "src/main.ts", 
						diff: "--- a/src/main.ts\n+++ b/src/main.ts\n@@ -1,3 +1,3 @@\n-console.log('old');\n+console.log('new');" 
					}
				}
			]
		})

		this.registerTool({
			name: "list_files",
			description: "List files and directories",
			category: "file_operations",
			parameters: [
				{
					name: "path",
					type: "string",
					description: "The directory path to list",
					required: false,
					default: "."
				},
				{
					name: "recursive",
					type: "boolean",
					description: "Whether to list recursively",
					required: false,
					default: false
				}
			],
			examples: [
				{
					description: "List current directory",
					parameters: {}
				},
				{
					description: "List specific directory recursively",
					parameters: { path: "src", recursive: true }
				}
			]
		})

		this.registerTool({
			name: "search_files",
			description: "Search for text within files",
			category: "search",
			parameters: [
				{
					name: "query",
					type: "string",
					description: "The search query",
					required: true
				},
				{
					name: "path",
					type: "string",
					description: "The path to search in",
					required: false,
					default: "."
				},
				{
					name: "file_pattern",
					type: "string",
					description: "File pattern to match (e.g., '*.ts')",
					required: false
				}
			],
			examples: [
				{
					description: "Search for function definition",
					parameters: { query: "function myFunction" }
				},
				{
					description: "Search in specific files",
					parameters: { query: "import", path: "src", file_pattern: "*.ts" }
				}
			]
		})

		this.registerTool({
			name: "insert_content",
			description: "Insert content at a specific line in a file",
			category: "file_operations",
			parameters: [
				{
					name: "path",
					type: "string",
					description: "The path to the file to modify",
					required: true
				},
				{
					name: "line",
					type: "string",
					description: "The line number after which to insert",
					required: true
				},
				{
					name: "content",
					type: "string",
					description: "The content to insert",
					required: true
				}
			],
			examples: [
				{
					description: "Insert code at specific line",
					parameters: { 
						path: "src/main.ts", 
						line: "5", 
						content: "console.log('inserted line');" 
					}
				}
			]
		})

		this.registerTool({
			name: "search_and_replace",
			description: "Search and replace text in files",
			category: "file_operations",
			parameters: [
				{
					name: "path",
					type: "string",
					description: "The path to search in",
					required: true
				},
				{
					name: "search",
					type: "string",
					description: "The text to search for",
					required: true
				},
				{
					name: "replace",
					type: "string",
					description: "The replacement text",
					required: true
				}
			],
			examples: [
				{
					description: "Replace text in file",
					parameters: { 
						path: "src/main.ts", 
						search: "oldFunction", 
						replace: "newFunction" 
					}
				}
			]
		})

		// Add more tools as needed...
	}

	public registerTool(tool: StandardToolDefinition): void {
		this.tools.set(tool.name, tool)
	}

	public getTool(name: ToolName): StandardToolDefinition | undefined {
		return this.tools.get(name)
	}

	public getAllTools(): StandardToolDefinition[] {
		return Array.from(this.tools.values())
	}

	public getToolsByCategory(category: string): StandardToolDefinition[] {
		return this.getAllTools().filter(tool => tool.category === category)
	}

	public getEnabledTools(enabledToolNames: ToolName[]): StandardToolDefinition[] {
		return enabledToolNames
			.map(name => this.getTool(name))
			.filter((tool): tool is StandardToolDefinition => tool !== undefined)
	}
}
