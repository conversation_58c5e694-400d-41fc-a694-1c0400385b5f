import { ToolExecutionStrategy, ToolExecutionMode } from "../../../shared/tools"
import { ToolName } from "@roo-code/types"
import { XmlToolExecutionStrategy } from "./XmlToolExecutionStrategy"
import { ClaudeNativeToolExecutionStrategy } from "./ClaudeNativeToolExecutionStrategy"
import { OpenAiNativeToolExecutionStrategy } from "./OpenAiNativeToolExecutionStrategy"
import { HybridToolExecutionStrategy } from "./HybridToolExecutionStrategy"
import { StandardToolRegistry } from "../schema/StandardToolSchema"
import { ToolRenderer } from "../renderers/ToolRenderer"

/**
 * Factory for creating appropriate tool execution strategies based on model capabilities
 */
export class ToolExecutionFactory {
	private static instance: ToolExecutionFactory
	private strategies: Map<ToolExecutionMode, ToolExecutionStrategy> = new Map()

	private constructor() {
		this.initializeStrategies()
	}

	public static getInstance(): ToolExecutionFactory {
		if (!ToolExecutionFactory.instance) {
			ToolExecutionFactory.instance = new ToolExecutionFactory()
		}
		return ToolExecutionFactory.instance
	}

	private initializeStrategies(): void {
		this.strategies.set("xml", new XmlToolExecutionStrategy())
		this.strategies.set("native", new ClaudeNativeToolExecutionStrategy())
		this.strategies.set("hybrid", new HybridToolExecutionStrategy())
	}

	/**
	 * Get the best tool execution strategy for a given model
	 */
	public getStrategy(modelProvider: string, modelId: string): ToolExecutionStrategy {
		// Prioritize native tool calling for supported models
		if (this.supportsNativeToolCalling(modelProvider, modelId)) {
			return this.strategies.get("hybrid")! // Use hybrid for intelligent fallback
		}

		// Fall back to XML for unsupported models
		return this.strategies.get("xml")!
	}

	/**
	 * Check if a model supports native tool calling
	 */
	public supportsNativeToolCalling(modelProvider: string, modelId: string): boolean {
		switch (modelProvider.toLowerCase()) {
			case "anthropic":
				return this.isClaudeModelWithToolSupport(modelId)
			case "openai":
			case "openai-native":
				return this.isOpenAiModelWithToolSupport(modelId)
			case "gemini":
				return this.isGeminiModelWithToolSupport(modelId)
			default:
				return false
		}
	}

	private isClaudeModelWithToolSupport(modelId: string): boolean {
		// Claude 3.5 Sonnet, Claude 3 Opus, and newer models support native tool calling
		const supportedModels = [
			"claude-3-5-sonnet",
			"claude-3-5-haiku",
			"claude-3-opus",
			"claude-3-sonnet",
			"claude-3-haiku",
			"claude-opus-4",
			"claude-sonnet-4",
		]
		
		return supportedModels.some(model => modelId.toLowerCase().includes(model))
	}

	private isOpenAiModelWithToolSupport(modelId: string): boolean {
		// GPT-4 and newer models support function calling
		const supportedModels = [
			"gpt-4",
			"gpt-4-turbo",
			"gpt-4o",
			"gpt-3.5-turbo",
			"o1",
			"o3",
		]
		
		return supportedModels.some(model => modelId.toLowerCase().includes(model))
	}

	private isGeminiModelWithToolSupport(modelId: string): boolean {
		// Gemini Pro and newer models support function calling
		const supportedModels = [
			"gemini-pro",
			"gemini-1.5",
			"gemini-2.0",
		]
		
		return supportedModels.some(model => modelId.toLowerCase().includes(model))
	}

	/**
	 * Get strategy for a specific tool execution mode
	 */
	public getStrategyByMode(mode: ToolExecutionMode): ToolExecutionStrategy {
		const strategy = this.strategies.get(mode)
		if (!strategy) {
			throw new Error(`No strategy found for mode: ${mode}`)
		}
		return strategy
	}

	/**
	 * Register a custom strategy
	 */
	public registerStrategy(mode: ToolExecutionMode, strategy: ToolExecutionStrategy): void {
		this.strategies.set(mode, strategy)
	}
}
