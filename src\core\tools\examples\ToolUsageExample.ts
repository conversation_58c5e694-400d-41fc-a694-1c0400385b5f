import { UnifiedToolManager } from "../UnifiedToolManager"
import { ToolExecutionHandler } from "../ToolExecutionHandler"
import { ToolName } from "@roo-code/types"

/**
 * Example demonstrating the new unified tool management approach
 */
export class ToolUsageExample {
	private toolManager = UnifiedToolManager.getInstance()
	private executionHandler = ToolExecutionHandler.getInstance()

	/**
	 * Example: Getting tools for different models
	 */
	public demonstrateToolConfiguration() {
		const enabledTools: ToolName[] = ["read_file", "write_to_file", "execute_command", "apply_diff"]

		console.log("=== Tool Configuration Examples ===\n")

		// Claude 3.5 Sonnet (supports native tools)
		console.log("1. Claude 3.5 Sonnet (Native Tool Calling):")
		const claudeTools = this.toolManager.getToolsForModel(enabledTools, "anthropic", "claude-3-5-sonnet")
		console.log(`   Type: ${claudeTools.type}`)
		console.log(`   Native tools count: ${claudeTools.claudeTools?.length || 0}`)
		console.log(`   Prompt preview: ${claudeTools.promptDescription.substring(0, 100)}...\n`)

		// GPT-4 (supports function calling)
		console.log("2. GPT-4 (Function Calling):")
		const openaiTools = this.toolManager.getToolsForModel(enabledTools, "openai", "gpt-4")
		console.log(`   Type: ${openaiTools.type}`)
		console.log(`   Functions count: ${openaiTools.openaiTools?.length || 0}`)
		console.log(`   Prompt preview: ${openaiTools.promptDescription.substring(0, 100)}...\n`)

		// Claude 2 (XML fallback)
		console.log("3. Claude 2 (XML Fallback):")
		const xmlTools = this.toolManager.getToolsForModel(enabledTools, "anthropic", "claude-2")
		console.log(`   Type: ${xmlTools.type}`)
		console.log(`   Prompt preview: ${xmlTools.promptDescription.substring(0, 100)}...\n`)
	}

	/**
	 * Example: Tool prompt generation
	 */
	public demonstratePromptGeneration() {
		const enabledTools: ToolName[] = ["read_file", "write_to_file"]

		console.log("=== Prompt Generation Examples ===\n")

		// Native tool calling prompt
		console.log("1. Native Tool Calling Prompt (Claude 3.5):")
		const nativePrompt = this.toolManager.getToolPromptDescription(
			enabledTools, 
			"anthropic", 
			"claude-3-5-sonnet"
		)
		console.log(nativePrompt)
		console.log("\n" + "=".repeat(50) + "\n")

		// XML tool calling prompt
		console.log("2. XML Tool Calling Prompt (Claude 2):")
		const xmlPrompt = this.toolManager.getToolPromptDescription(
			enabledTools, 
			"anthropic", 
			"claude-2"
		)
		console.log(xmlPrompt)
		console.log("\n" + "=".repeat(50) + "\n")
	}

	/**
	 * Example: Tool execution with telemetry
	 */
	public async demonstrateToolExecution() {
		console.log("=== Tool Execution Examples ===\n")

		// Mock context for demonstration
		const mockContext = {
			cline: {
				providerRef: {
					deref: () => ({
						getApiProvider: () => "anthropic",
						getModel: () => ({ id: "claude-3-5-sonnet" })
					})
				}
			},
			askApproval: async () => true,
			handleError: (error: Error) => console.error("Tool error:", error),
			pushToolResult: (result: any) => console.log("Tool result:", result),
			removeClosingTag: () => {}
		}

		try {
			// Example 1: Read file
			console.log("1. Executing read_file tool:")
			const readResult = await this.executionHandler.executeToolCall(
				"read_file",
				{ path: "package.json", start_line: 1, end_line: 10 },
				mockContext
			)
			console.log(`   Success: ${readResult.success}`)
			console.log(`   Execution mode: ${readResult.executionMode}`)
			console.log(`   Execution time: ${readResult.executionTime}ms\n`)

			// Example 2: Write file
			console.log("2. Executing write_to_file tool:")
			const writeResult = await this.executionHandler.executeToolCall(
				"write_to_file",
				{ path: "test.txt", content: "Hello, World!" },
				mockContext
			)
			console.log(`   Success: ${writeResult.success}`)
			console.log(`   Execution mode: ${writeResult.executionMode}`)
			console.log(`   Execution time: ${writeResult.executionTime}ms\n`)

		} catch (error) {
			console.error("Execution failed:", error)
		}
	}

	/**
	 * Example: Telemetry insights
	 */
	public demonstrateTelemetryInsights() {
		console.log("=== Telemetry Insights ===\n")

		const insights = this.executionHandler.getTelemetryInsights()
		
		console.log(`Total executions: ${insights.totalExecutions}`)
		console.log(`Overall success rate: ${insights.overallSuccessRate.toFixed(1)}%`)
		
		if (insights.problematicTools.length > 0) {
			console.log("\nProblematic tools:")
			insights.problematicTools.forEach(tool => {
				console.log(`  - ${tool.toolName} on ${tool.modelProvider}: ${tool.failureRate.toFixed(1)}% failure rate`)
			})
		}

		if (insights.recommendations.length > 0) {
			console.log("\nRecommendations:")
			insights.recommendations.forEach(rec => {
				console.log(`  - ${rec}`)
			})
		}

		console.log("\nNative vs XML Performance:")
		Object.entries(insights.nativeVsXmlPerformance).forEach(([key, perf]) => {
			if (perf.native && perf.xml) {
				console.log(`  ${key}:`)
				console.log(`    Native: ${perf.native.successRate.toFixed(1)}% success, ${perf.native.averageExecutionTime.toFixed(0)}ms avg`)
				console.log(`    XML: ${perf.xml.successRate.toFixed(1)}% success, ${perf.xml.averageExecutionTime.toFixed(0)}ms avg`)
			}
		})
	}

	/**
	 * Example: Model capability detection
	 */
	public demonstrateCapabilityDetection() {
		console.log("=== Model Capability Detection ===\n")

		const testCases = [
			{ provider: "anthropic", model: "claude-3-5-sonnet-20241022" },
			{ provider: "anthropic", model: "claude-2.1" },
			{ provider: "openai", model: "gpt-4-turbo" },
			{ provider: "openai", model: "gpt-3.5-turbo" },
			{ provider: "openai", model: "text-davinci-003" },
			{ provider: "gemini", model: "gemini-pro" },
			{ provider: "unknown", model: "some-model" }
		]

		testCases.forEach(({ provider, model }) => {
			const supportsNative = this.toolManager.supportsNativeToolCalling(provider, model)
			console.log(`${provider}/${model}: ${supportsNative ? "✅ Native tools" : "❌ XML fallback"}`)
		})
	}

	/**
	 * Run all examples
	 */
	public async runAllExamples() {
		console.log("🚀 Unified Tool Manager Examples\n")
		
		this.demonstrateCapabilityDetection()
		console.log("\n" + "=".repeat(60) + "\n")
		
		this.demonstrateToolConfiguration()
		console.log("=".repeat(60) + "\n")
		
		this.demonstratePromptGeneration()
		
		// Note: Tool execution examples would require actual tool implementations
		// this.demonstrateToolExecution()
		// this.demonstrateTelemetryInsights()
		
		console.log("✅ Examples completed!")
	}
}

// Usage example
if (require.main === module) {
	const example = new ToolUsageExample()
	example.runAllExamples().catch(console.error)
}
