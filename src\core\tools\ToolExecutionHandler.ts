import { ToolName } from "@roo-code/types"
import { UnifiedToolManager, ToolsForModel } from "./UnifiedToolManager"
import { ToolExecutionTelemetry, ToolExecutionEvent } from "./telemetry/ToolExecutionTelemetry"

/**
 * Simplified tool execution handler that replaces the complex strategy pattern
 */
export class ToolExecutionHandler {
	private static instance: ToolExecutionHandler
	private toolManager = UnifiedToolManager.getInstance()
	private telemetry = ToolExecutionTelemetry.getInstance()

	public static getInstance(): ToolExecutionHandler {
		if (!ToolExecutionHandler.instance) {
			ToolExecutionHandler.instance = new ToolExecutionHandler()
		}
		return ToolExecutionHandler.instance
	}

	/**
	 * Get tools configuration for a specific model
	 */
	public getToolsForModel(
		enabledTools: ToolName[],
		modelProvider: string,
		modelId: string
	): ToolsForModel {
		return this.toolManager.getToolsForModel(enabledTools, modelProvider, modelId)
	}

	/**
	 * Get tool prompt description for a specific model
	 */
	public getToolPromptDescription(
		enabledTools: ToolName[],
		modelProvider: string,
		modelId: string
	): string {
		return this.toolManager.getToolPromptDescription(enabledTools, modelProvider, modelId)
	}

	/**
	 * Execute a tool call and record telemetry
	 */
	public async executeToolCall(
		toolName: ToolName,
		params: Record<string, any>,
		context: ToolExecutionContext
	): Promise<ToolExecutionResult> {
		const { cline } = context
		const provider = cline.providerRef.deref()
		const modelProvider = provider?.getApiProvider() || "unknown"
		const modelId = provider?.getModel()?.id || "unknown"
		
		const startTime = Date.now()
		const supportsNative = this.toolManager.supportsNativeToolCalling(modelProvider, modelId)
		const executionMode = supportsNative ? "native" : "xml"

		try {
			// Execute the tool using existing tool logic
			const result = await this.executeExistingToolLogic(toolName, params, context)
			
			const executionTime = Date.now() - startTime

			// Record successful execution
			const event: ToolExecutionEvent = {
				toolName,
				modelProvider,
				executionMode,
				success: true,
				executionTime,
				timestamp: new Date()
			}
			this.telemetry.recordExecution(event)

			return {
				success: true,
				result,
				executionMode,
				executionTime
			}
		} catch (error) {
			const executionTime = Date.now() - startTime

			// Record failed execution
			const event: ToolExecutionEvent = {
				toolName,
				modelProvider,
				executionMode,
				success: false,
				executionTime,
				error: error as Error,
				timestamp: new Date()
			}
			this.telemetry.recordExecution(event)

			return {
				success: false,
				result: null,
				executionMode,
				executionTime,
				error: error as Error
			}
		}
	}

	/**
	 * Execute tool using existing tool logic
	 * This bridges to the current implementation while we transition
	 */
	private async executeExistingToolLogic(
		toolName: ToolName,
		params: Record<string, any>,
		context: ToolExecutionContext
	): Promise<any> {
		const { cline, askApproval, handleError, pushToolResult, removeClosingTag } = context

		// Create a tool use block in the expected format
		const block = {
			type: "tool_use" as const,
			name: toolName,
			params: params,
			partial: false
		}

		// Execute using existing tool logic from presentAssistantMessage.ts
		switch (toolName) {
			case "write_to_file":
				const { writeToFileTool } = await import("../assistant-message/tools/writeToFileTool")
				return await writeToFileTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "read_file":
				const { readFileTool } = await import("../assistant-message/tools/readFileTool")
				return await readFileTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "execute_command":
				const { executeCommandTool } = await import("../assistant-message/tools/executeCommandTool")
				return await executeCommandTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "apply_diff":
				const { applyDiffTool } = await import("../assistant-message/tools/multiApplyDiffTool")
				return await applyDiffTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "list_files":
				const { listFilesTool } = await import("../assistant-message/tools/listFilesTool")
				return await listFilesTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "search_files":
				const { searchFilesTool } = await import("../assistant-message/tools/searchFilesTool")
				return await searchFilesTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "insert_content":
				const { insertContentTool } = await import("../assistant-message/tools/insertContentTool")
				return await insertContentTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "search_and_replace":
				const { searchAndReplaceTool } = await import("../assistant-message/tools/searchAndReplaceTool")
				return await searchAndReplaceTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "list_code_definition_names":
				const { listCodeDefinitionNamesTool } = await import("../assistant-message/tools/listCodeDefinitionNamesTool")
				return await listCodeDefinitionNamesTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "browser_action":
				const { browserActionTool } = await import("../assistant-message/tools/browserActionTool")
				return await browserActionTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "use_mcp_tool":
				const { useMcpToolTool } = await import("../assistant-message/tools/useMcpToolTool")
				return await useMcpToolTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "access_mcp_resource":
				const { accessMcpResourceTool } = await import("../assistant-message/tools/accessMcpResourceTool")
				return await accessMcpResourceTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "ask_followup_question":
				const { askFollowupQuestionTool } = await import("../assistant-message/tools/askFollowupQuestionTool")
				return await askFollowupQuestionTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "attempt_completion":
				const { attemptCompletionTool } = await import("../assistant-message/tools/attemptCompletionTool")
				// Note: attemptCompletionTool has additional parameters, this is simplified
				return await attemptCompletionTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!, () => "", () => Promise.resolve(true))
			
			case "switch_mode":
				const { switchModeTool } = await import("../assistant-message/tools/switchModeTool")
				return await switchModeTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "new_task":
				const { newTaskTool } = await import("../assistant-message/tools/newTaskTool")
				return await newTaskTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "fetch_instructions":
				const { fetchInstructionsTool } = await import("../assistant-message/tools/fetchInstructionsTool")
				return await fetchInstructionsTool(cline, block, askApproval, handleError, pushToolResult)
			
			case "codebase_search":
				const { codebaseSearchTool } = await import("../assistant-message/tools/codebaseSearchTool")
				return await codebaseSearchTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			case "update_todo_list":
				const { updateTodoListTool } = await import("../assistant-message/tools/updateTodoListTool")
				return await updateTodoListTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
			
			default:
				throw new Error(`Unknown tool: ${toolName}`)
		}
	}

	/**
	 * Get telemetry insights
	 */
	public getTelemetryInsights() {
		return this.telemetry.getPerformanceInsights()
	}

	/**
	 * Check if a tool should prefer fallback based on telemetry
	 */
	public shouldPreferFallback(toolName: ToolName, modelProvider: string): boolean {
		return this.telemetry.shouldPreferFallback(toolName, modelProvider)
	}
}

// Type definitions
export interface ToolExecutionContext {
	cline: any // Task instance
	askApproval: any
	handleError: any
	pushToolResult: any
	removeClosingTag?: any
}

export interface ToolExecutionResult {
	success: boolean
	result: any
	executionMode: "native" | "xml"
	executionTime: number
	error?: Error
}
