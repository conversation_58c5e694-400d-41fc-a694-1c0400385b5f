import { Tool<PERSON><PERSON> } from "../../../shared/tools"
import { Anthropic } from "@anthropic-ai/sdk"
import OpenAI from "openai"

/**
 * Generates tool definitions for different AI providers
 */
export class ToolDefinitionGenerator {
	private static instance: ToolDefinitionGenerator

	public static getInstance(): ToolDefinitionGenerator {
		if (!ToolDefinitionGenerator.instance) {
			ToolDefinitionGenerator.instance = new ToolDefinitionGenerator()
		}
		return ToolDefinitionGenerator.instance
	}

	/**
	 * Generate Claude native tool definitions
	 */
	public generateClaudeTools(enabledTools: ToolName[]): Anthropic.Tool[] {
		const claudeTools: Anthropic.Tool[] = []

		if (enabledTools.includes("read_file") || enabledTools.includes("list_files")) {
			claudeTools.push({
				name: "view",
				description: "View the contents of a file or directory",
				input_schema: {
					type: "object",
					properties: {
						path: {
							type: "string",
							description: "The path to the file or directory to view"
						},
						view_range: {
							type: "array",
							items: { type: "integer" },
							description: "Optional range [start_line, end_line] for viewing specific lines"
						}
					},
					required: ["path"]
				}
			})
		}

		if (enabledTools.includes("write_to_file")) {
			claudeTools.push({
				name: "create",
				description: "Create a new file with the given content",
				input_schema: {
					type: "object",
					properties: {
						path: {
							type: "string",
							description: "The path where the file should be created"
						},
						file_text: {
							type: "string",
							description: "The content to write to the file"
						}
					},
					required: ["path", "file_text"]
				}
			})
		}

		if (enabledTools.includes("apply_diff")) {
			claudeTools.push({
				name: "str_replace",
				description: "Replace text in a file using exact string matching",
				input_schema: {
					type: "object",
					properties: {
						path: {
							type: "string",
							description: "The path to the file to modify"
						},
						old_str: {
							type: "string",
							description: "The exact string to replace"
						},
						new_str: {
							type: "string",
							description: "The replacement string"
						}
					},
					required: ["path", "old_str", "new_str"]
				}
			})
		}

		if (enabledTools.includes("insert_content")) {
			claudeTools.push({
				name: "insert",
				description: "Insert text at a specific line in a file",
				input_schema: {
					type: "object",
					properties: {
						path: {
							type: "string",
							description: "The path to the file to modify"
						},
						insert_line: {
							type: "integer",
							description: "The line number after which to insert the text"
						},
						new_str: {
							type: "string",
							description: "The text to insert"
						}
					},
					required: ["path", "insert_line", "new_str"]
				}
			})
		}

		return claudeTools
	}

	/**
	 * Generate OpenAI function definitions
	 */
	public generateOpenAiFunctions(enabledTools: ToolName[]): OpenAI.Chat.Completions.ChatCompletionTool[] {
		const functions: OpenAI.Chat.Completions.ChatCompletionTool[] = []

		if (enabledTools.includes("read_file")) {
			functions.push({
				type: "function",
				function: {
					name: "read_file",
					description: "Read the contents of a file",
					parameters: {
						type: "object",
						properties: {
							path: {
								type: "string",
								description: "The path to the file to read"
							},
							start_line: {
								type: "integer",
								description: "The starting line number (1-based)"
							},
							end_line: {
								type: "integer",
								description: "The ending line number (1-based)"
							}
						},
						required: ["path"]
					}
				}
			})
		}

		if (enabledTools.includes("write_to_file")) {
			functions.push({
				type: "function",
				function: {
					name: "write_to_file",
					description: "Write content to a file",
					parameters: {
						type: "object",
						properties: {
							path: {
								type: "string",
								description: "The path to the file to write"
							},
							content: {
								type: "string",
								description: "The content to write to the file"
							}
						},
						required: ["path", "content"]
					}
				}
			})
		}

		if (enabledTools.includes("execute_command")) {
			functions.push({
				type: "function",
				function: {
					name: "execute_command",
					description: "Execute a shell command",
					parameters: {
						type: "object",
						properties: {
							command: {
								type: "string",
								description: "The command to execute"
							},
							cwd: {
								type: "string",
								description: "The working directory for the command"
							}
						},
						required: ["command"]
					}
				}
			})
		}

		if (enabledTools.includes("apply_diff")) {
			functions.push({
				type: "function",
				function: {
					name: "apply_diff",
					description: "Apply a diff patch to a file",
					parameters: {
						type: "object",
						properties: {
							path: {
								type: "string",
								description: "The path to the file to modify"
							},
							diff: {
								type: "string",
								description: "The diff content to apply"
							}
						},
						required: ["path", "diff"]
					}
				}
			})
		}

		if (enabledTools.includes("list_files")) {
			functions.push({
				type: "function",
				function: {
					name: "list_files",
					description: "List files and directories",
					parameters: {
						type: "object",
						properties: {
							path: {
								type: "string",
								description: "The directory path to list"
							},
							recursive: {
								type: "boolean",
								description: "Whether to list recursively"
							}
						},
						required: []
					}
				}
			})
		}

		if (enabledTools.includes("search_files")) {
			functions.push({
				type: "function",
				function: {
					name: "search_files",
					description: "Search for text within files",
					parameters: {
						type: "object",
						properties: {
							query: {
								type: "string",
								description: "The search query"
							},
							path: {
								type: "string",
								description: "The path to search in"
							}
						},
						required: ["query"]
					}
				}
			})
		}

		if (enabledTools.includes("insert_content")) {
			functions.push({
				type: "function",
				function: {
					name: "insert_content",
					description: "Insert content at a specific line in a file",
					parameters: {
						type: "object",
						properties: {
							path: {
								type: "string",
								description: "The path to the file to modify"
							},
							line: {
								type: "string",
								description: "The line number after which to insert"
							},
							content: {
								type: "string",
								description: "The content to insert"
							}
						},
						required: ["path", "line", "content"]
					}
				}
			})
		}

		if (enabledTools.includes("search_and_replace")) {
			functions.push({
				type: "function",
				function: {
					name: "search_and_replace",
					description: "Search and replace text in files",
					parameters: {
						type: "object",
						properties: {
							path: {
								type: "string",
								description: "The path to search in"
							},
							search: {
								type: "string",
								description: "The text to search for"
							},
							replace: {
								type: "string",
								description: "The replacement text"
							}
						},
						required: ["path", "search", "replace"]
					}
				}
			})
		}

		return functions
	}

	/**
	 * Generate XML tool descriptions for legacy mode
	 */
	public generateXmlToolDescriptions(enabledTools: ToolName[]): string {
		const descriptions: string[] = []

		enabledTools.forEach(toolName => {
			const description = this.getXmlToolDescription(toolName)
			if (description) {
				descriptions.push(description)
			}
		})

		return descriptions.join("\n\n")
	}

	private getXmlToolDescription(toolName: ToolName): string | null {
		switch (toolName) {
			case "read_file":
				return `## read_file
Description: Read the contents of a file.

Parameters:
- path: (required) The path to the file to read
- start_line: (optional) The starting line number (1-based)
- end_line: (optional) The ending line number (1-based)

Usage:
<read_file>
<path>file_path</path>
<start_line>1</start_line>
<end_line>10</end_line>
</read_file>`

			case "write_to_file":
				return `## write_to_file
Description: Write content to a file.

Parameters:
- path: (required) The path to the file to write
- content: (required) The content to write to the file

Usage:
<write_to_file>
<path>file_path</path>
<content>file content here</content>
</write_to_file>`

			case "execute_command":
				return `## execute_command
Description: Execute a shell command.

Parameters:
- command: (required) The command to execute
- cwd: (optional) The working directory for the command

Usage:
<execute_command>
<command>ls -la</command>
<cwd>/path/to/directory</cwd>
</execute_command>`

			default:
				return null
		}
	}
}
