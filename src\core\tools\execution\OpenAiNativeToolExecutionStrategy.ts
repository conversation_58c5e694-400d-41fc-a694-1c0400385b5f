import { 
	ToolExecutionStrategy, 
	ToolExecutionMode, 
	ToolName, 
	ToolCall, 
	ToolExecutionContext, 
	ToolResponse,
	ToolDefinition
} from "../../../shared/tools"

/**
 * Native tool execution strategy for OpenAI GPT models
 * Uses OpenAI's function calling API instead of XML tags
 */
export class OpenAiNativeToolExecutionStrategy implements ToolExecutionStrategy {
	readonly mode: ToolExecutionMode = "native"

	canExecute(toolName: ToolName, modelProvider: string, modelId: string): boolean {
		// Only works with OpenAI models that support function calling
		const supportedProviders = ["openai", "openai-native"]
		return supportedProviders.includes(modelProvider.toLowerCase()) && this.isOpenAiModelWithToolSupport(modelId)
	}

	async executeToolCall(toolCall: ToolCall, context: ToolExecutionContext): Promise<ToolResponse> {
		// Map Roo Code tool to OpenAI function call format
		const openAiFunction = this.mapToOpenAiFunction(toolCall)
		
		// Execute the tool using OpenAI's function calling format
		return this.executeOpenAiFunction(openAiFunction, context)
	}

	formatToolsForPrompt(tools: ToolDefinition[]): string {
		// OpenAI functions are passed via the functions parameter in the API call
		// The system prompt doesn't need tool descriptions in native mode
		return "# Tools\n\nYou have access to functions that will be provided via the native function calling interface."
	}

	private isOpenAiModelWithToolSupport(modelId: string): boolean {
		const supportedModels = [
			"gpt-4",
			"gpt-4-turbo", 
			"gpt-4o",
			"gpt-3.5-turbo",
			"o1",
			"o3",
		]
		
		return supportedModels.some(model => modelId.toLowerCase().includes(model))
	}

	private mapToOpenAiFunction(toolCall: ToolCall): OpenAiFunctionCall {
		// Map Roo Code tools to OpenAI function format
		// OpenAI uses JSON Schema for parameter definitions
		
		const functionDefinition = this.getOpenAiFunctionDefinition(toolCall.name)
		
		return {
			name: functionDefinition.name,
			description: functionDefinition.description,
			parameters: functionDefinition.parameters,
			arguments: toolCall.params
		}
	}

	private getOpenAiFunctionDefinition(toolName: ToolName): OpenAiFunctionDefinition {
		// Define OpenAI function schemas for each Roo Code tool
		switch (toolName) {
			case "read_file":
				return {
					name: "read_file",
					description: "Read the contents of a file",
					parameters: {
						type: "object",
						properties: {
							path: {
								type: "string",
								description: "The path to the file to read"
							},
							start_line: {
								type: "integer",
								description: "The starting line number (1-based)"
							},
							end_line: {
								type: "integer", 
								description: "The ending line number (1-based)"
							}
						},
						required: ["path"]
					}
				}
			
			case "write_to_file":
				return {
					name: "write_to_file",
					description: "Write content to a file",
					parameters: {
						type: "object",
						properties: {
							path: {
								type: "string",
								description: "The path to the file to write"
							},
							content: {
								type: "string",
								description: "The content to write to the file"
							}
						},
						required: ["path", "content"]
					}
				}
			
			case "execute_command":
				return {
					name: "execute_command",
					description: "Execute a shell command",
					parameters: {
						type: "object",
						properties: {
							command: {
								type: "string",
								description: "The command to execute"
							},
							cwd: {
								type: "string",
								description: "The working directory for the command"
							}
						},
						required: ["command"]
					}
				}
			
			case "apply_diff":
				return {
					name: "apply_diff",
					description: "Apply a diff patch to a file",
					parameters: {
						type: "object",
						properties: {
							path: {
								type: "string",
								description: "The path to the file to modify"
							},
							diff: {
								type: "string",
								description: "The diff content to apply"
							}
						},
						required: ["path", "diff"]
					}
				}
			
			case "list_files":
				return {
					name: "list_files",
					description: "List files and directories",
					parameters: {
						type: "object",
						properties: {
							path: {
								type: "string",
								description: "The directory path to list"
							},
							recursive: {
								type: "boolean",
								description: "Whether to list recursively"
							}
						},
						required: []
					}
				}
			
			case "search_files":
				return {
					name: "search_files",
					description: "Search for text within files",
					parameters: {
						type: "object",
						properties: {
							query: {
								type: "string",
								description: "The search query"
							},
							path: {
								type: "string",
								description: "The path to search in"
							}
						},
						required: ["query"]
					}
				}
			
			default:
				// Generic function definition for unknown tools
				return {
					name: toolName,
					description: `Execute ${toolName} tool`,
					parameters: {
						type: "object",
						properties: {},
						required: []
					}
				}
		}
	}

	private async executeOpenAiFunction(functionCall: OpenAiFunctionCall, context: ToolExecutionContext): Promise<ToolResponse> {
		// Execute the tool using OpenAI's function calling format
		// This integrates with the enhanced OpenAiHandler

		const { cline } = context
		const provider = cline.providerRef.deref()

		if (!provider || !provider.supportsNativeFunctionCalling || !provider.supportsNativeFunctionCalling()) {
			// Fall back to XML strategy if native functions not supported
			const xmlStrategy = new (await import("./XmlToolExecutionStrategy")).XmlToolExecutionStrategy()
			const originalToolCall: ToolCall = {
				name: functionCall.name as ToolName,
				params: functionCall.arguments
			}
			return xmlStrategy.executeToolCall(originalToolCall, context)
		}

		// Map OpenAI function back to Roo Code tool for execution
		const rooCodeTool: ToolCall = {
			name: functionCall.name as ToolName,
			params: functionCall.arguments
		}

		// Execute using the existing tool logic but with native function context
		const xmlStrategy = new (await import("./XmlToolExecutionStrategy")).XmlToolExecutionStrategy()
		return xmlStrategy.executeToolCall(rooCodeTool, context)
	}
}

interface OpenAiFunctionCall {
	name: string
	description: string
	parameters: any
	arguments: Record<string, any>
}

interface OpenAiFunctionDefinition {
	name: string
	description: string
	parameters: {
		type: "object"
		properties: Record<string, any>
		required: string[]
	}
}
