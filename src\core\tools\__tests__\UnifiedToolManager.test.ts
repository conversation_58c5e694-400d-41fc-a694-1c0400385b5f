import { UnifiedToolManager } from "../UnifiedToolManager"
import { ToolName } from "@roo-code/types"

describe("UnifiedToolManager", () => {
	let toolManager: UnifiedToolManager

	beforeEach(() => {
		toolManager = UnifiedToolManager.getInstance()
	})

	describe("supportsNativeToolCalling", () => {
		it("should return true for Claude models with tool support", () => {
			expect(toolManager.supportsNativeToolCalling("anthropic", "claude-3-5-sonnet-20241022")).toBe(true)
			expect(toolManager.supportsNativeToolCalling("anthropic", "claude-3-opus-20240229")).toBe(true)
			expect(toolManager.supportsNativeToolCalling("anthropic", "claude-3-haiku-20240307")).toBe(true)
		})

		it("should return true for OpenAI models with function calling", () => {
			expect(toolManager.supportsNativeToolCalling("openai", "gpt-4-turbo")).toBe(true)
			expect(toolManager.supportsNativeToolCalling("openai", "gpt-4o")).toBe(true)
			expect(toolManager.supportsNativeToolCalling("openai", "gpt-3.5-turbo")).toBe(true)
		})

		it("should return false for unsupported models", () => {
			expect(toolManager.supportsNativeToolCalling("anthropic", "claude-2")).toBe(false)
			expect(toolManager.supportsNativeToolCalling("openai", "gpt-3")).toBe(false)
			expect(toolManager.supportsNativeToolCalling("unknown", "model")).toBe(false)
		})
	})

	describe("getToolsForModel", () => {
		const enabledTools: ToolName[] = ["read_file", "write_to_file", "execute_command"]

		it("should return native tools for supported models", () => {
			const result = toolManager.getToolsForModel(enabledTools, "anthropic", "claude-3-5-sonnet")
			
			expect(result.type).toBe("native")
			expect(result.claudeTools).toBeDefined()
			expect(result.claudeTools).toHaveLength(3) // read_file maps to view, write_to_file maps to create, execute_command not directly supported
			expect(result.promptDescription).toContain("Claude's native tool calling interface")
		})

		it("should return XML tools for unsupported models", () => {
			const result = toolManager.getToolsForModel(enabledTools, "anthropic", "claude-2")
			
			expect(result.type).toBe("xml")
			expect(result.claudeTools).toBeUndefined()
			expect(result.openaiTools).toBeUndefined()
			expect(result.promptDescription).toContain("XML tags")
		})

		it("should return OpenAI functions for OpenAI models", () => {
			const result = toolManager.getToolsForModel(enabledTools, "openai", "gpt-4")
			
			expect(result.type).toBe("native")
			expect(result.openaiTools).toBeDefined()
			expect(result.openaiTools).toHaveLength(3)
			expect(result.promptDescription).toContain("OpenAI's native tool calling interface")
		})
	})

	describe("getToolPromptDescription", () => {
		const enabledTools: ToolName[] = ["read_file", "write_to_file"]

		it("should return native description for supported models", () => {
			const description = toolManager.getToolPromptDescription(enabledTools, "anthropic", "claude-3-5-sonnet")
			
			expect(description).toContain("Claude's native tool calling interface")
			expect(description).toContain("No XML tags are required")
			expect(description).not.toContain("<read_file>")
		})

		it("should return XML description for unsupported models", () => {
			const description = toolManager.getToolPromptDescription(enabledTools, "anthropic", "claude-2")
			
			expect(description).toContain("XML tags")
			expect(description).toContain("<read_file>")
			expect(description).toContain("<write_to_file>")
		})
	})

	describe("isToolSupportedByProvider", () => {
		it("should return true for basic file operations", () => {
			expect(toolManager.isToolSupportedByProvider("read_file", "anthropic")).toBe(true)
			expect(toolManager.isToolSupportedByProvider("write_to_file", "openai")).toBe(true)
			expect(toolManager.isToolSupportedByProvider("execute_command", "anthropic")).toBe(true)
		})

		it("should return true for browser actions on supported providers", () => {
			expect(toolManager.isToolSupportedByProvider("browser_action", "anthropic")).toBe(true)
			expect(toolManager.isToolSupportedByProvider("browser_action", "openai")).toBe(true)
		})

		it("should return true for MCP tools on all providers", () => {
			expect(toolManager.isToolSupportedByProvider("use_mcp_tool", "anthropic")).toBe(true)
			expect(toolManager.isToolSupportedByProvider("use_mcp_tool", "openai")).toBe(true)
			expect(toolManager.isToolSupportedByProvider("access_mcp_resource", "unknown")).toBe(true)
		})
	})
})

describe("Tool Rendering Integration", () => {
	let toolManager: UnifiedToolManager

	beforeEach(() => {
		toolManager = UnifiedToolManager.getInstance()
	})

	it("should render consistent tool definitions across formats", () => {
		const enabledTools: ToolName[] = ["read_file", "write_to_file"]
		
		// Get tools for different providers
		const claudeTools = toolManager.getToolsForModel(enabledTools, "anthropic", "claude-3-5-sonnet")
		const openaiTools = toolManager.getToolsForModel(enabledTools, "openai", "gpt-4")
		const xmlTools = toolManager.getToolsForModel(enabledTools, "anthropic", "claude-2")

		// Verify Claude native tools
		expect(claudeTools.type).toBe("native")
		expect(claudeTools.claudeTools).toHaveLength(2) // view and create
		
		// Verify OpenAI functions
		expect(openaiTools.type).toBe("native")
		expect(openaiTools.openaiTools).toHaveLength(2) // read_file and write_to_file
		
		// Verify XML fallback
		expect(xmlTools.type).toBe("xml")
		expect(xmlTools.promptDescription).toContain("read_file")
		expect(xmlTools.promptDescription).toContain("write_to_file")
	})

	it("should provide appropriate examples in XML format", () => {
		const xmlDescription = toolManager.getToolPromptDescription(
			["read_file", "write_to_file"], 
			"anthropic", 
			"claude-2"
		)

		expect(xmlDescription).toContain("<read_file>")
		expect(xmlDescription).toContain("<path>")
		expect(xmlDescription).toContain("</read_file>")
		expect(xmlDescription).toContain("<write_to_file>")
		expect(xmlDescription).toContain("<content>")
		expect(xmlDescription).toContain("</write_to_file>")
	})

	it("should not include XML examples in native tool descriptions", () => {
		const nativeDescription = toolManager.getToolPromptDescription(
			["read_file", "write_to_file"], 
			"anthropic", 
			"claude-3-5-sonnet"
		)

		expect(nativeDescription).not.toContain("<read_file>")
		expect(nativeDescription).not.toContain("<write_to_file>")
		expect(nativeDescription).toContain("native tool calling")
	})
})
