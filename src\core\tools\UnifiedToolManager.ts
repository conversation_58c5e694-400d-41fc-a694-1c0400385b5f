import { ToolName } from "@roo-code/types"
import { StandardToolRegistry, StandardToolDefinition } from "./schema/StandardToolSchema"
import { ToolRenderer } from "./renderers/ToolRenderer"
import { Anthropic } from "@anthropic-ai/sdk"
import OpenAI from "openai"

/**
 * Unified tool manager that handles tool definitions and rendering
 * This replaces the complex strategy pattern with a simpler approach
 */
export class UnifiedToolManager {
	private static instance: UnifiedToolManager
	private toolRegistry = StandardToolRegistry.getInstance()
	private renderer = ToolRenderer.getInstance()

	public static getInstance(): UnifiedToolManager {
		if (!UnifiedToolManager.instance) {
			UnifiedToolManager.instance = new UnifiedToolManager()
		}
		return UnifiedToolManager.instance
	}

	/**
	 * Check if a model supports native tool calling
	 */
	public supportsNativeToolCalling(modelProvider: string, modelId: string): boolean {
		switch (modelProvider.toLowerCase()) {
			case "anthropic":
				return this.isClaudeModelWithToolSupport(modelId)
			case "openai":
			case "openai-native":
				return this.isOpenAiModelWithToolSupport(modelId)
			case "gemini":
				return this.isGeminiModelWithToolSupport(modelId)
			default:
				return false
		}
	}

	/**
	 * Get the appropriate tool format for a model
	 */
	public getToolsForModel(
		enabledTools: ToolName[], 
		modelProvider: string, 
		modelId: string
	): ToolsForModel {
		const standardTools = this.toolRegistry.getEnabledTools(enabledTools)
		
		if (this.supportsNativeToolCalling(modelProvider, modelId)) {
			return this.getNativeTools(standardTools, modelProvider)
		} else {
			return this.getXMLTools(standardTools)
		}
	}

	/**
	 * Get prompt description for tools
	 */
	public getToolPromptDescription(
		enabledTools: ToolName[], 
		modelProvider: string, 
		modelId: string
	): string {
		const standardTools = this.toolRegistry.getEnabledTools(enabledTools)
		
		if (this.supportsNativeToolCalling(modelProvider, modelId)) {
			const providerName = this.getProviderDisplayName(modelProvider)
			return this.renderer.renderAsNativePrompt(standardTools, providerName)
		} else {
			return this.renderer.renderAsXMLPrompt(standardTools)
		}
	}

	/**
	 * Get native tools for API calls
	 */
	private getNativeTools(tools: StandardToolDefinition[], modelProvider: string): ToolsForModel {
		switch (modelProvider.toLowerCase()) {
			case "anthropic":
				return {
					type: "native",
					claudeTools: this.renderer.renderAsClaudeTools(tools),
					promptDescription: this.renderer.renderAsNativePrompt(tools, "Claude")
				}
			case "openai":
			case "openai-native":
				return {
					type: "native",
					openaiTools: this.renderer.renderAsOpenAIFunctions(tools),
					promptDescription: this.renderer.renderAsNativePrompt(tools, "OpenAI")
				}
			default:
				// Fallback to XML for unsupported providers
				return this.getXMLTools(tools)
		}
	}

	/**
	 * Get XML tools for legacy models
	 */
	private getXMLTools(tools: StandardToolDefinition[]): ToolsForModel {
		return {
			type: "xml",
			promptDescription: this.renderer.renderAsXMLPrompt(tools)
		}
	}

	private getProviderDisplayName(modelProvider: string): string {
		switch (modelProvider.toLowerCase()) {
			case "anthropic":
				return "Claude"
			case "openai":
			case "openai-native":
				return "OpenAI"
			case "gemini":
				return "Gemini"
			default:
				return modelProvider
		}
	}

	private isClaudeModelWithToolSupport(modelId: string): boolean {
		const supportedModels = [
			"claude-3-5-sonnet",
			"claude-3-5-haiku",
			"claude-3-opus",
			"claude-3-sonnet",
			"claude-3-haiku",
			"claude-opus-4",
			"claude-sonnet-4",
		]
		
		return supportedModels.some(model => modelId.toLowerCase().includes(model))
	}

	private isOpenAiModelWithToolSupport(modelId: string): boolean {
		const supportedModels = [
			"gpt-4",
			"gpt-4-turbo",
			"gpt-4o",
			"gpt-3.5-turbo",
			"o1",
			"o3",
		]
		
		return supportedModels.some(model => modelId.toLowerCase().includes(model))
	}

	private isGeminiModelWithToolSupport(modelId: string): boolean {
		const supportedModels = [
			"gemini-pro",
			"gemini-1.5",
			"gemini-2.0",
		]
		
		return supportedModels.some(model => modelId.toLowerCase().includes(model))
	}

	/**
	 * Get all available tools
	 */
	public getAllAvailableTools(): StandardToolDefinition[] {
		return this.toolRegistry.getAllTools()
	}

	/**
	 * Get tools by category
	 */
	public getToolsByCategory(category: string): StandardToolDefinition[] {
		return this.toolRegistry.getToolsByCategory(category)
	}

	/**
	 * Register a custom tool
	 */
	public registerCustomTool(tool: StandardToolDefinition): void {
		this.toolRegistry.registerTool(tool)
	}

	/**
	 * Check if a specific tool is supported by a provider
	 */
	public isToolSupportedByProvider(toolName: ToolName, modelProvider: string): boolean {
		// Most tools are supported by all providers
		// Only specific tools might have limitations
		
		switch (toolName) {
			case "browser_action":
				// Browser actions might not be supported by all providers
				return ["anthropic", "openai"].includes(modelProvider.toLowerCase())
			case "use_mcp_tool":
			case "access_mcp_resource":
				// MCP tools are provider-agnostic
				return true
			default:
				return true
		}
	}
}

// Type definitions
export interface ToolsForModel {
	type: "native" | "xml"
	claudeTools?: Anthropic.Tool[]
	openaiTools?: OpenAI.Chat.Completions.ChatCompletionTool[]
	promptDescription: string
}

export interface ToolExecutionConfig {
	enabledTools: ToolName[]
	modelProvider: string
	modelId: string
	useNativeToolCalling?: boolean // Override automatic detection
}
