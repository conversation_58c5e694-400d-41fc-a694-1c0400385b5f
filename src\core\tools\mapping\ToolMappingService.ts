import { ToolName } from "../../../shared/tools"

/**
 * Service for mapping between Roo Code tools and provider-specific native tool formats
 */
export class ToolMappingService {
	private static instance: ToolMappingService

	public static getInstance(): ToolMappingService {
		if (!ToolMappingService.instance) {
			ToolMappingService.instance = new ToolMappingService()
		}
		return ToolMappingService.instance
	}

	/**
	 * Map Roo Code tool to Claude native tool format
	 */
	public mapRooCodeToClaudeNative(toolName: ToolName, params: Record<string, any>): ClaudeNativeToolCall {
		switch (toolName) {
			case "read_file":
				return {
					name: "view",
					parameters: {
						path: params.path,
						...(params.start_line && params.end_line ? {
							view_range: [parseInt(params.start_line), parseInt(params.end_line)]
						} : {})
					}
				}

			case "write_to_file":
				return {
					name: "create",
					parameters: {
						path: params.path,
						file_text: params.content
					}
				}

			case "apply_diff":
				// Convert diff to str_replace format
				const { oldStr, newStr } = this.parseDiffToStrReplace(params.diff)
				return {
					name: "str_replace",
					parameters: {
						path: params.path,
						old_str: oldStr,
						new_str: newStr
					}
				}

			case "insert_content":
				return {
					name: "insert",
					parameters: {
						path: params.path,
						insert_line: parseInt(params.line),
						new_str: params.content
					}
				}

			case "list_files":
				return {
					name: "view",
					parameters: {
						path: params.path || "."
					}
				}

			case "search_files":
				// Claude doesn't have a direct search equivalent, use view with path
				return {
					name: "view",
					parameters: {
						path: params.path || "."
					}
				}

			default:
				// For tools without direct mapping, use generic approach
				return {
					name: toolName,
					parameters: params
				}
		}
	}

	/**
	 * Map Claude native tool back to Roo Code format
	 */
	public mapClaudeNativeToRooCode(claudeCall: ClaudeNativeToolCall): RooCodeToolCall {
		switch (claudeCall.name) {
			case "view":
				if (claudeCall.parameters.view_range) {
					return {
						name: "read_file",
						params: {
							path: claudeCall.parameters.path,
							start_line: claudeCall.parameters.view_range[0]?.toString(),
							end_line: claudeCall.parameters.view_range[1]?.toString()
						}
					}
				} else {
					return {
						name: "list_files",
						params: {
							path: claudeCall.parameters.path
						}
					}
				}

			case "create":
				return {
					name: "write_to_file",
					params: {
						path: claudeCall.parameters.path,
						content: claudeCall.parameters.file_text
					}
				}

			case "str_replace":
				return {
					name: "apply_diff",
					params: {
						path: claudeCall.parameters.path,
						diff: this.createDiffFromStrReplace(
							claudeCall.parameters.old_str,
							claudeCall.parameters.new_str
						)
					}
				}

			case "insert":
				return {
					name: "insert_content",
					params: {
						path: claudeCall.parameters.path,
						line: claudeCall.parameters.insert_line.toString(),
						content: claudeCall.parameters.new_str
					}
				}

			default:
				return {
					name: claudeCall.name as ToolName,
					params: claudeCall.parameters
				}
		}
	}

	/**
	 * Map Roo Code tool to OpenAI function format
	 */
	public mapRooCodeToOpenAiFunction(toolName: ToolName, params: Record<string, any>): OpenAiFunctionCall {
		// OpenAI functions map directly to Roo Code tools in most cases
		return {
			name: toolName,
			arguments: params
		}
	}

	/**
	 * Map OpenAI function back to Roo Code format
	 */
	public mapOpenAiFunctionToRooCode(functionCall: OpenAiFunctionCall): RooCodeToolCall {
		return {
			name: functionCall.name as ToolName,
			params: functionCall.arguments
		}
	}

	/**
	 * Get tool compatibility matrix
	 */
	public getToolCompatibility(): ToolCompatibilityMatrix {
		return {
			claude: {
				supported: ["read_file", "write_to_file", "apply_diff", "insert_content", "list_files"],
				mappings: {
					"read_file": "view",
					"write_to_file": "create",
					"apply_diff": "str_replace",
					"insert_content": "insert",
					"list_files": "view"
				}
			},
			openai: {
				supported: ["read_file", "write_to_file", "apply_diff", "insert_content", "list_files", "search_files", "execute_command"],
				mappings: {
					"read_file": "read_file",
					"write_to_file": "write_to_file",
					"apply_diff": "apply_diff",
					"insert_content": "insert_content",
					"list_files": "list_files",
					"search_files": "search_files",
					"execute_command": "execute_command"
				}
			}
		}
	}

	/**
	 * Check if a tool is supported by a provider
	 */
	public isToolSupportedByProvider(toolName: ToolName, provider: "claude" | "openai"): boolean {
		const compatibility = this.getToolCompatibility()
		return compatibility[provider].supported.includes(toolName)
	}

	/**
	 * Parse diff content to extract old and new strings for str_replace
	 */
	private parseDiffToStrReplace(diff: string): { oldStr: string; newStr: string } {
		const lines = diff.split('\n')
		let oldStr = ""
		let newStr = ""
		
		for (const line of lines) {
			if (line.startsWith('-') && !line.startsWith('---')) {
				oldStr += line.substring(1) + '\n'
			} else if (line.startsWith('+') && !line.startsWith('+++')) {
				newStr += line.substring(1) + '\n'
			}
		}
		
		return { 
			oldStr: oldStr.trim(), 
			newStr: newStr.trim() 
		}
	}

	/**
	 * Create diff format from old and new strings
	 */
	private createDiffFromStrReplace(oldStr: string, newStr: string): string {
		const oldLines = oldStr.split('\n')
		const newLines = newStr.split('\n')
		
		let diff = "--- a/file\n+++ b/file\n@@ -1," + oldLines.length + " +1," + newLines.length + " @@\n"
		
		oldLines.forEach(line => {
			diff += "-" + line + "\n"
		})
		
		newLines.forEach(line => {
			diff += "+" + line + "\n"
		})
		
		return diff
	}
}

// Type definitions
export interface ClaudeNativeToolCall {
	name: string
	parameters: Record<string, any>
}

export interface OpenAiFunctionCall {
	name: string
	arguments: Record<string, any>
}

export interface RooCodeToolCall {
	name: ToolName
	params: Record<string, any>
}

export interface ToolCompatibilityMatrix {
	claude: {
		supported: ToolName[]
		mappings: Record<string, string>
	}
	openai: {
		supported: ToolName[]
		mappings: Record<string, string>
	}
}
