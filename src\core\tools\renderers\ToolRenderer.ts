import { StandardToolDefinition, StandardToolParameter } from "../schema/StandardToolSchema"
import { Anthropic } from "@anthropic-ai/sdk"
import OpenAI from "openai"

/**
 * Renders standard tool definitions to different formats
 */
export class ToolRenderer {
	private static instance: ToolRenderer

	public static getInstance(): ToolRenderer {
		if (!ToolRenderer.instance) {
			ToolRenderer.instance = new ToolRenderer()
		}
		return ToolRenderer.instance
	}

	/**
	 * Render tools as Claude native tools
	 */
	public renderAsClaudeTools(tools: StandardToolDefinition[]): Anthropic.Tool[] {
		return tools.map(tool => ({
			name: tool.name,
			description: tool.description,
			input_schema: {
				type: "object" as const,
				properties: this.convertParametersToClaudeProperties(tool.parameters),
				required: tool.parameters.filter(p => p.required).map(p => p.name)
			}
		}))
	}

	/**
	 * Render tools as OpenAI functions
	 */
	public renderAsOpenAIFunctions(tools: StandardToolDefinition[]): OpenAI.Chat.Completions.ChatCompletionTool[] {
		return tools.map(tool => ({
			type: "function" as const,
			function: {
				name: tool.name,
				description: tool.description,
				parameters: {
					type: "object" as const,
					properties: this.convertParametersToOpenAIProperties(tool.parameters),
					required: tool.parameters.filter(p => p.required).map(p => p.name)
				}
			}
		}))
	}

	/**
	 * Render tools as XML descriptions for prompt
	 */
	public renderAsXMLPrompt(tools: StandardToolDefinition[]): string {
		const toolDescriptions = tools.map(tool => this.renderToolAsXML(tool))
		return `# Tools

You have access to the following tools. Use them by wrapping your tool calls in XML tags as shown in the examples.

${toolDescriptions.join('\n\n')}`
	}

	/**
	 * Render tools as simple descriptions for native tool calling models
	 */
	public renderAsNativePrompt(tools: StandardToolDefinition[], providerName: string): string {
		const toolList = tools.map(tool => `- **${tool.name}**: ${tool.description}`).join('\n')
		
		return `# Tools

You have access to tools through ${providerName}'s native tool calling interface. The following tools are available:

${toolList}

Use these tools as needed to complete your tasks. The tools will be provided through the native interface, so you don't need to use XML tags.

## Important Notes:
- Tools are called using the native ${providerName} tool calling format
- No XML tags are required - the system will handle tool invocation automatically
- Tool responses will be provided directly in the conversation
- You can call multiple tools in sequence as needed`
	}

	private renderToolAsXML(tool: StandardToolDefinition): string {
		const parameterDescriptions = tool.parameters.map(param => {
			const requiredText = param.required ? " (required)" : " (optional)"
			const defaultText = param.default !== undefined ? ` [default: ${param.default}]` : ""
			const enumText = param.enum ? ` [options: ${param.enum.join(', ')}]` : ""
			return `- ${param.name}: ${param.description}${requiredText}${defaultText}${enumText}`
		}).join('\n')

		const exampleXML = this.generateExampleXML(tool)

		return `## ${tool.name}
${tool.description}

Parameters:
${parameterDescriptions}

Usage:
${exampleXML}`
	}

	private generateExampleXML(tool: StandardToolDefinition): string {
		const example = tool.examples?.[0]
		if (example) {
			const parameterTags = Object.entries(example.parameters)
				.map(([name, value]) => `<${name}>${value}</${name}>`)
				.join('\n')
			
			return `<${tool.name}>
${parameterTags}
</${tool.name}>`
		}

		// Generate example from parameter definitions
		const parameterTags = tool.parameters
			.filter(p => p.required)
			.map(p => `<${p.name}>${this.getExampleValue(p)}</${p.name}>`)
			.join('\n')

		return `<${tool.name}>
${parameterTags}
</${tool.name}>`
	}

	private getExampleValue(param: StandardToolParameter): string {
		if (param.default !== undefined) {
			return String(param.default)
		}

		switch (param.type) {
			case "string":
				return param.enum ? param.enum[0] : "example_value"
			case "number":
				return "123"
			case "boolean":
				return "true"
			case "array":
				return "item1,item2"
			case "object":
				return "{}"
			default:
				return "value"
		}
	}

	private convertParametersToClaudeProperties(parameters: StandardToolParameter[]): Record<string, any> {
		const properties: Record<string, any> = {}

		parameters.forEach(param => {
			properties[param.name] = {
				type: this.mapTypeToClaudeType(param.type),
				description: param.description
			}

			if (param.enum) {
				properties[param.name].enum = param.enum
			}

			if (param.type === "array" && param.items) {
				properties[param.name].items = {
					type: this.mapTypeToClaudeType(param.items.type),
					description: param.items.description
				}
			}

			if (param.type === "object" && param.properties) {
				properties[param.name].properties = this.convertParametersToClaudeProperties(param.properties)
			}
		})

		return properties
	}

	private convertParametersToOpenAIProperties(parameters: StandardToolParameter[]): Record<string, any> {
		const properties: Record<string, any> = {}

		parameters.forEach(param => {
			properties[param.name] = {
				type: this.mapTypeToOpenAIType(param.type),
				description: param.description
			}

			if (param.enum) {
				properties[param.name].enum = param.enum
			}

			if (param.type === "array" && param.items) {
				properties[param.name].items = {
					type: this.mapTypeToOpenAIType(param.items.type),
					description: param.items.description
				}
			}

			if (param.type === "object" && param.properties) {
				properties[param.name].properties = this.convertParametersToOpenAIProperties(param.properties)
				properties[param.name].required = param.properties.filter(p => p.required).map(p => p.name)
			}
		})

		return properties
	}

	private mapTypeToClaudeType(type: string): string {
		switch (type) {
			case "number":
				return "integer"
			default:
				return type
		}
	}

	private mapTypeToOpenAIType(type: string): string {
		switch (type) {
			case "number":
				return "integer"
			default:
				return type
		}
	}
}
