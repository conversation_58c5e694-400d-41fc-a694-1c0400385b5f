import { ToolName, ToolExecutionMode } from "../../../shared/tools"

/**
 * Telemetry service for tracking tool execution performance and failures
 */
export class ToolExecutionTelemetry {
	private static instance: ToolExecutionTelemetry
	private executionStats = new Map<string, ToolExecutionStats>()
	private recentExecutions: ToolExecutionEvent[] = []
	private maxRecentExecutions = 1000

	public static getInstance(): ToolExecutionTelemetry {
		if (!ToolExecutionTelemetry.instance) {
			ToolExecutionTelemetry.instance = new ToolExecutionTelemetry()
		}
		return ToolExecutionTelemetry.instance
	}

	/**
	 * Record a tool execution attempt
	 */
	public recordExecution(event: ToolExecutionEvent): void {
		const key = this.getStatsKey(event.toolName, event.modelProvider, event.executionMode)
		
		// Update aggregated stats
		const stats = this.executionStats.get(key) || this.createEmptyStats()
		stats.totalAttempts++
		stats.lastExecutionTime = event.timestamp

		if (event.success) {
			stats.successCount++
			stats.totalExecutionTime += event.executionTime || 0
			if (event.executionTime) {
				stats.averageExecutionTime = stats.totalExecutionTime / stats.successCount
			}
		} else {
			stats.failureCount++
			stats.lastFailureReason = event.error?.message
			stats.lastFailureTime = event.timestamp
		}

		if (event.fallbackUsed) {
			stats.fallbackCount++
		}

		this.executionStats.set(key, stats)

		// Add to recent executions
		this.recentExecutions.push(event)
		if (this.recentExecutions.length > this.maxRecentExecutions) {
			this.recentExecutions.shift()
		}

		// Log significant events
		this.logSignificantEvents(event, stats)
	}

	/**
	 * Get execution statistics for a specific tool/provider/mode combination
	 */
	public getStats(toolName: ToolName, modelProvider: string, executionMode: ToolExecutionMode): ToolExecutionStats | null {
		const key = this.getStatsKey(toolName, modelProvider, executionMode)
		return this.executionStats.get(key) || null
	}

	/**
	 * Get all execution statistics
	 */
	public getAllStats(): Map<string, ToolExecutionStats> {
		return new Map(this.executionStats)
	}

	/**
	 * Get failure rate for a specific tool/provider combination
	 */
	public getFailureRate(toolName: ToolName, modelProvider: string, executionMode?: ToolExecutionMode): number {
		if (executionMode) {
			const stats = this.getStats(toolName, modelProvider, executionMode)
			if (!stats || stats.totalAttempts === 0) return 0
			return (stats.failureCount / stats.totalAttempts) * 100
		}

		// Calculate overall failure rate across all execution modes
		let totalAttempts = 0
		let totalFailures = 0

		for (const [key, stats] of this.executionStats.entries()) {
			if (key.includes(`${toolName}:${modelProvider}`)) {
				totalAttempts += stats.totalAttempts
				totalFailures += stats.failureCount
			}
		}

		if (totalAttempts === 0) return 0
		return (totalFailures / totalAttempts) * 100
	}

	/**
	 * Check if a tool should prefer fallback based on recent failure history
	 */
	public shouldPreferFallback(toolName: ToolName, modelProvider: string): boolean {
		const nativeFailureRate = this.getFailureRate(toolName, modelProvider, "native")
		const xmlFailureRate = this.getFailureRate(toolName, modelProvider, "xml")
		
		// Prefer fallback if native failure rate is significantly higher than XML
		const threshold = 25 // 25% higher failure rate
		return nativeFailureRate > xmlFailureRate + threshold && nativeFailureRate > 50
	}

	/**
	 * Get recent execution events for analysis
	 */
	public getRecentExecutions(limit?: number): ToolExecutionEvent[] {
		const events = [...this.recentExecutions].reverse() // Most recent first
		return limit ? events.slice(0, limit) : events
	}

	/**
	 * Get performance insights
	 */
	public getPerformanceInsights(): PerformanceInsights {
		const insights: PerformanceInsights = {
			totalExecutions: 0,
			overallSuccessRate: 0,
			nativeVsXmlPerformance: {},
			problematicTools: [],
			recommendations: []
		}

		let totalAttempts = 0
		let totalSuccesses = 0

		// Analyze all stats
		for (const [key, stats] of this.executionStats.entries()) {
			totalAttempts += stats.totalAttempts
			totalSuccesses += stats.successCount

			const [toolName, modelProvider, executionMode] = key.split(':')
			const failureRate = (stats.failureCount / stats.totalAttempts) * 100

			// Track problematic tools
			if (failureRate > 30 && stats.totalAttempts >= 5) {
				insights.problematicTools.push({
					toolName: toolName as ToolName,
					modelProvider,
					executionMode: executionMode as ToolExecutionMode,
					failureRate,
					totalAttempts: stats.totalAttempts
				})
			}

			// Compare native vs XML performance
			const baseKey = `${toolName}:${modelProvider}`
			if (!insights.nativeVsXmlPerformance[baseKey]) {
				insights.nativeVsXmlPerformance[baseKey] = { native: null, xml: null }
			}

			if (executionMode === "native") {
				insights.nativeVsXmlPerformance[baseKey].native = {
					successRate: (stats.successCount / stats.totalAttempts) * 100,
					averageExecutionTime: stats.averageExecutionTime
				}
			} else if (executionMode === "xml") {
				insights.nativeVsXmlPerformance[baseKey].xml = {
					successRate: (stats.successCount / stats.totalAttempts) * 100,
					averageExecutionTime: stats.averageExecutionTime
				}
			}
		}

		insights.totalExecutions = totalAttempts
		insights.overallSuccessRate = totalAttempts > 0 ? (totalSuccesses / totalAttempts) * 100 : 0

		// Generate recommendations
		insights.recommendations = this.generateRecommendations(insights)

		return insights
	}

	/**
	 * Reset all statistics
	 */
	public reset(): void {
		this.executionStats.clear()
		this.recentExecutions = []
	}

	/**
	 * Export telemetry data for analysis
	 */
	public exportData(): TelemetryExport {
		return {
			timestamp: new Date().toISOString(),
			stats: Object.fromEntries(this.executionStats),
			recentExecutions: this.recentExecutions,
			insights: this.getPerformanceInsights()
		}
	}

	private getStatsKey(toolName: ToolName, modelProvider: string, executionMode: ToolExecutionMode): string {
		return `${toolName}:${modelProvider}:${executionMode}`
	}

	private createEmptyStats(): ToolExecutionStats {
		return {
			totalAttempts: 0,
			successCount: 0,
			failureCount: 0,
			fallbackCount: 0,
			totalExecutionTime: 0,
			averageExecutionTime: 0,
			lastExecutionTime: new Date(),
			lastFailureTime: null,
			lastFailureReason: null
		}
	}

	private logSignificantEvents(event: ToolExecutionEvent, stats: ToolExecutionStats): void {
		const failureRate = (stats.failureCount / stats.totalAttempts) * 100

		// Log when failure rate crosses thresholds
		if (stats.totalAttempts === 10 && failureRate > 50) {
			console.warn(`High failure rate detected for ${event.toolName} on ${event.modelProvider}: ${failureRate.toFixed(1)}%`)
		}

		// Log when fallback is frequently used
		if (stats.totalAttempts > 0 && (stats.fallbackCount / stats.totalAttempts) > 0.5) {
			console.info(`Frequent fallback usage for ${event.toolName} on ${event.modelProvider}: ${((stats.fallbackCount / stats.totalAttempts) * 100).toFixed(1)}%`)
		}
	}

	private generateRecommendations(insights: PerformanceInsights): string[] {
		const recommendations: string[] = []

		// Recommend disabling native tools for problematic combinations
		insights.problematicTools.forEach(tool => {
			if (tool.executionMode === "native" && tool.failureRate > 50) {
				recommendations.push(`Consider disabling native tool calling for ${tool.toolName} on ${tool.modelProvider} (${tool.failureRate.toFixed(1)}% failure rate)`)
			}
		})

		// Recommend enabling native tools where they perform better
		Object.entries(insights.nativeVsXmlPerformance).forEach(([key, performance]) => {
			if (performance.native && performance.xml) {
				const nativeSuccessRate = performance.native.successRate
				const xmlSuccessRate = performance.xml.successRate
				
				if (nativeSuccessRate > xmlSuccessRate + 10) {
					recommendations.push(`Native tool calling shows better performance for ${key} (+${(nativeSuccessRate - xmlSuccessRate).toFixed(1)}% success rate)`)
				}
			}
		})

		if (insights.overallSuccessRate < 80) {
			recommendations.push("Overall tool execution success rate is below 80%. Consider reviewing tool implementations.")
		}

		return recommendations
	}
}

// Type definitions
export interface ToolExecutionEvent {
	toolName: ToolName
	modelProvider: string
	executionMode: ToolExecutionMode
	success: boolean
	executionTime?: number
	fallbackUsed?: boolean
	error?: Error
	timestamp: Date
}

export interface ToolExecutionStats {
	totalAttempts: number
	successCount: number
	failureCount: number
	fallbackCount: number
	totalExecutionTime: number
	averageExecutionTime: number
	lastExecutionTime: Date
	lastFailureTime: Date | null
	lastFailureReason: string | null
}

export interface PerformanceInsights {
	totalExecutions: number
	overallSuccessRate: number
	nativeVsXmlPerformance: Record<string, {
		native: { successRate: number; averageExecutionTime: number } | null
		xml: { successRate: number; averageExecutionTime: number } | null
	}>
	problematicTools: Array<{
		toolName: ToolName
		modelProvider: string
		executionMode: ToolExecutionMode
		failureRate: number
		totalAttempts: number
	}>
	recommendations: string[]
}

export interface TelemetryExport {
	timestamp: string
	stats: Record<string, ToolExecutionStats>
	recentExecutions: ToolExecutionEvent[]
	insights: PerformanceInsights
}
