import {
	ToolExecutionStrategy,
	ToolExecutionMode,
	ToolName,
	ToolCall,
	ToolExecutionContext,
	ToolResponse,
	ToolDefinition,
	ToolExecutionResult
} from "../../../shared/tools"
import { ClaudeNativeToolExecutionStrategy } from "./ClaudeNativeToolExecutionStrategy"
import { OpenAiNativeToolExecutionStrategy } from "./OpenAiNativeToolExecutionStrategy"
import { XmlToolExecutionStrategy } from "./XmlToolExecutionStrategy"
import { ToolExecutionTelemetry, ToolExecutionEvent } from "../telemetry/ToolExecutionTelemetry"

/**
 * Hybrid tool execution strategy that tries native tool calling first,
 * then falls back to XML if native fails
 */
export class HybridToolExecutionStrategy implements ToolExecutionStrategy {
	readonly mode: ToolExecutionMode = "hybrid"

	private claudeStrategy = new ClaudeNativeToolExecutionStrategy()
	private openAiStrategy = new OpenAiNativeToolExecutionStrategy()
	private xmlStrategy = new XmlToolExecutionStrategy()
	private telemetry = ToolExecutionTelemetry.getInstance()

	// Track failure rates for telemetry (deprecated - now using ToolExecutionTelemetry)
	private failureStats = new Map<string, { attempts: number; failures: number }>()

	canExecute(toolName: ToolName, modelProvider: string, modelId: string): boolean {
		// Hybrid strategy can work with any model
		return true
	}

	async executeToolCall(toolCall: ToolCall, context: ToolExecutionContext): Promise<ToolResponse> {
		const { cline } = context
		const provider = cline.providerRef.deref()
		const modelProvider = provider?.getApiProvider() || "unknown"
		const modelId = provider?.getModel()?.id || "unknown"

		const startTime = Date.now()

		// Check if we should prefer fallback based on telemetry
		if (this.telemetry.shouldPreferFallback(toolCall.name, modelProvider)) {
			console.info(`Using XML fallback for ${toolCall.name} on ${modelProvider} based on failure history`)
			return this.executeWithTelemetry(
				() => this.xmlStrategy.executeToolCall(toolCall, context),
				toolCall,
				modelProvider,
				"xml",
				startTime,
				false
			)
		}

		// Try native tool calling first if supported
		const nativeStrategy = this.getNativeStrategy(modelProvider, modelId)

		if (nativeStrategy && nativeStrategy.canExecute(toolCall.name, modelProvider, modelId)) {
			try {
				// Attempt native tool execution
				const result = await this.executeWithTelemetry(
					() => nativeStrategy.executeToolCall(toolCall, context),
					toolCall,
					modelProvider,
					"native",
					startTime,
					false
				)

				return result
			} catch (error) {
				console.warn(`Native tool calling failed for ${toolCall.name}, falling back to XML:`, error)

				// Fall back to XML strategy
				return this.executeWithTelemetry(
					() => this.xmlStrategy.executeToolCall(toolCall, context),
					toolCall,
					modelProvider,
					"xml",
					Date.now(), // New start time for fallback
					true,
					error as Error
				)
			}
		}

		// Use XML strategy directly if native is not supported
		return this.executeWithTelemetry(
			() => this.xmlStrategy.executeToolCall(toolCall, context),
			toolCall,
			modelProvider,
			"xml",
			startTime,
			false
		)
	}

	formatToolsForPrompt(tools: ToolDefinition[]): string {
		// For hybrid mode, we need to determine which format to use based on the model
		// This would be called during prompt generation, so we need context about the model
		// For now, we'll use a generic approach that works with both
		
		return `# Tools

You have access to tools for various tasks. Tools will be provided via the most appropriate interface for your model (native tool calling or XML tags).

Available tools:
${tools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n')}

Use tools as needed to complete your tasks.`
	}

	private getNativeStrategy(modelProvider: string, modelId: string): ToolExecutionStrategy | null {
		if (this.claudeStrategy.canExecute("read_file" as ToolName, modelProvider, modelId)) {
			return this.claudeStrategy
		}
		
		if (this.openAiStrategy.canExecute("read_file" as ToolName, modelProvider, modelId)) {
			return this.openAiStrategy
		}
		
		return null
	}

	private async executeWithFallback(
		toolCall: ToolCall, 
		context: ToolExecutionContext, 
		originalError: Error
	): Promise<ToolResponse> {
		try {
			const result = await this.xmlStrategy.executeToolCall(toolCall, context)
			
			// Log successful fallback for telemetry
			console.log(`Successfully executed ${toolCall.name} using XML fallback after native failure`)
			
			return result
		} catch (fallbackError) {
			// Both native and XML failed
			console.error(`Both native and XML execution failed for ${toolCall.name}:`, {
				nativeError: originalError,
				xmlError: fallbackError
			})
			
			throw new Error(`Tool execution failed: Native error: ${originalError.message}, XML fallback error: ${fallbackError.message}`)
		}
	}

	private recordAttempt(toolName: ToolName, modelProvider: string, failed: boolean): void {
		const key = `${modelProvider}:${toolName}`
		const stats = this.failureStats.get(key) || { attempts: 0, failures: 0 }
		
		stats.attempts++
		if (failed) {
			stats.failures++
		}
		
		this.failureStats.set(key, stats)
		
		// Log telemetry data (could be sent to analytics service)
		if (stats.attempts % 10 === 0) {
			const failureRate = (stats.failures / stats.attempts) * 100
			console.log(`Tool execution stats for ${key}: ${failureRate.toFixed(1)}% failure rate (${stats.failures}/${stats.attempts})`)
		}
	}

	/**
	 * Get failure statistics for telemetry
	 */
	public getFailureStats(): Map<string, { attempts: number; failures: number; failureRate: number }> {
		const result = new Map()
		
		for (const [key, stats] of this.failureStats.entries()) {
			result.set(key, {
				...stats,
				failureRate: (stats.failures / stats.attempts) * 100
			})
		}
		
		return result
	}

	/**
	 * Reset failure statistics
	 */
	public resetStats(): void {
		this.failureStats.clear()
	}

	/**
	 * Check if a tool should prefer XML over native based on failure history
	 */
	private shouldPreferXml(toolName: ToolName, modelProvider: string): boolean {
		const key = `${modelProvider}:${toolName}`
		const stats = this.failureStats.get(key)

		if (!stats || stats.attempts < 5) {
			return false // Not enough data, try native
		}

		const failureRate = (stats.failures / stats.attempts) * 100
		return failureRate > 50 // Prefer XML if failure rate > 50%
	}

	/**
	 * Execute a tool call with telemetry tracking
	 */
	private async executeWithTelemetry(
		executor: () => Promise<ToolResponse>,
		toolCall: ToolCall,
		modelProvider: string,
		executionMode: ToolExecutionMode,
		startTime: number,
		fallbackUsed: boolean,
		originalError?: Error
	): Promise<ToolResponse> {
		try {
			const result = await executor()
			const executionTime = Date.now() - startTime

			// Record successful execution
			const event: ToolExecutionEvent = {
				toolName: toolCall.name,
				modelProvider,
				executionMode,
				success: true,
				executionTime,
				fallbackUsed,
				timestamp: new Date()
			}
			this.telemetry.recordExecution(event)

			// Also update legacy stats
			this.recordAttempt(toolCall.name, modelProvider, false)

			return result
		} catch (error) {
			const executionTime = Date.now() - startTime

			// Record failed execution
			const event: ToolExecutionEvent = {
				toolName: toolCall.name,
				modelProvider,
				executionMode,
				success: false,
				executionTime,
				fallbackUsed,
				error: error as Error,
				timestamp: new Date()
			}
			this.telemetry.recordExecution(event)

			// Also update legacy stats
			this.recordAttempt(toolCall.name, modelProvider, true)

			throw error
		}
	}
}
