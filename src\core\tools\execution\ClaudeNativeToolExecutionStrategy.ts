import { 
	ToolExecutionStrategy, 
	ToolExecutionMode, 
	ToolName, 
	ToolCall, 
	ToolExecutionContext, 
	ToolResponse,
	ToolDefinition
} from "../../../shared/tools"

/**
 * Native tool execution strategy for Anthropic Claude models
 * Uses <PERSON>'s native tool calling API instead of XML tags
 */
export class ClaudeNativeToolExecutionStrategy implements ToolExecutionStrategy {
	readonly mode: ToolExecutionMode = "native"

	canExecute(toolName: ToolName, modelProvider: string, modelId: string): boolean {
		// Only works with Anthropic Claude models that support native tool calling
		return modelProvider.toLowerCase() === "anthropic" && this.isClaudeModelWithToolSupport(modelId)
	}

	async executeToolCall(toolCall: ToolCall, context: ToolExecutionContext): Promise<ToolResponse> {
		// Map Roo Code tool to Claude native tool format
		const claudeToolCall = this.mapToClaudeToolCall(toolCall)
		
		// Execute the tool using <PERSON>'s native format
		return this.executeClaudeNativeTool(claudeToolCall, context)
	}

	formatToolsForPrompt(tools: ToolDefinition[]): string {
		// Claude native tools are passed via the tools parameter in the API call
		// The system prompt doesn't need tool descriptions in native mode
		return "# Tools\n\nYou have access to tools that will be provided via the native tool calling interface."
	}

	private isClaudeModelWithToolSupport(modelId: string): boolean {
		const supportedModels = [
			"claude-3-5-sonnet",
			"claude-3-5-haiku", 
			"claude-3-opus",
			"claude-3-sonnet",
			"claude-3-haiku",
			"claude-opus-4",
			"claude-sonnet-4",
		]
		
		return supportedModels.some(model => modelId.toLowerCase().includes(model))
	}

	private mapToClaudeToolCall(toolCall: ToolCall): ClaudeNativeToolCall {
		// Map Roo Code tools to Claude's native tool format
		switch (toolCall.name) {
			case "read_file":
				return this.mapReadFileToClaudeView(toolCall)
			case "write_to_file":
				return this.mapWriteFileToClaudeCreate(toolCall)
			case "apply_diff":
				return this.mapApplyDiffToClaudeStrReplace(toolCall)
			case "insert_content":
				return this.mapInsertContentToClaudeInsert(toolCall)
			case "list_files":
				return this.mapListFilesToClaudeView(toolCall)
			default:
				// For tools without direct Claude equivalents, use a generic approach
				return {
					name: toolCall.name,
					parameters: toolCall.params
				}
		}
	}

	private mapReadFileToClaudeView(toolCall: ToolCall): ClaudeNativeToolCall {
		const { path, start_line, end_line } = toolCall.params
		
		return {
			name: "view",
			parameters: {
				path: path,
				...(start_line && end_line ? { view_range: [parseInt(start_line), parseInt(end_line)] } : {})
			}
		}
	}

	private mapWriteFileToClaudeCreate(toolCall: ToolCall): ClaudeNativeToolCall {
		const { path, content } = toolCall.params
		
		return {
			name: "create",
			parameters: {
				path: path,
				file_text: content
			}
		}
	}

	private mapApplyDiffToClaudeStrReplace(toolCall: ToolCall): ClaudeNativeToolCall {
		// This is a complex mapping - apply_diff needs to be converted to str_replace
		// For now, we'll use a simplified approach
		const { path, diff } = toolCall.params
		
		// Parse the diff to extract old and new content
		const { oldStr, newStr } = this.parseDiffContent(diff)
		
		return {
			name: "str_replace",
			parameters: {
				path: path,
				old_str: oldStr,
				new_str: newStr
			}
		}
	}

	private mapInsertContentToClaudeInsert(toolCall: ToolCall): ClaudeNativeToolCall {
		const { path, line, content } = toolCall.params
		
		return {
			name: "insert",
			parameters: {
				path: path,
				insert_line: parseInt(line),
				new_str: content
			}
		}
	}

	private mapListFilesToClaudeView(toolCall: ToolCall): ClaudeNativeToolCall {
		const { path } = toolCall.params
		
		return {
			name: "view",
			parameters: {
				path: path || "."
			}
		}
	}

	private parseDiffContent(diff: string): { oldStr: string; newStr: string } {
		// Simple diff parsing - this would need to be more sophisticated in practice
		// For now, we'll extract the content between diff markers
		const lines = diff.split('\n')
		let oldStr = ""
		let newStr = ""
		let inOld = false
		let inNew = false
		
		for (const line of lines) {
			if (line.startsWith('---')) {
				inOld = false
				inNew = false
			} else if (line.startsWith('+++')) {
				inOld = false
				inNew = false
			} else if (line.startsWith('-')) {
				oldStr += line.substring(1) + '\n'
				inOld = true
			} else if (line.startsWith('+')) {
				newStr += line.substring(1) + '\n'
				inNew = true
			} else if (!line.startsWith('@@')) {
				if (inOld) oldStr += line + '\n'
				if (inNew) newStr += line + '\n'
			}
		}
		
		return { oldStr: oldStr.trim(), newStr: newStr.trim() }
	}

	private async executeClaudeNativeTool(claudeToolCall: ClaudeNativeToolCall, context: ToolExecutionContext): Promise<ToolResponse> {
		// Execute the tool using Claude's native tool format
		// This integrates with the enhanced AnthropicHandler

		const { cline } = context
		const provider = cline.providerRef.deref()

		if (!provider || !provider.supportsNativeToolCalling || !provider.supportsNativeToolCalling()) {
			// Fall back to XML strategy if native tools not supported
			const xmlStrategy = new (await import("./XmlToolExecutionStrategy")).XmlToolExecutionStrategy()
			const originalToolCall: ToolCall = {
				name: claudeToolCall.name as ToolName,
				params: claudeToolCall.parameters
			}
			return xmlStrategy.executeToolCall(originalToolCall, context)
		}

		// Map Claude native tool back to Roo Code tool for execution
		const rooCodeTool = this.mapClaudeToolToRooCode(claudeToolCall)

		// Execute using the existing tool logic but with native tool context
		const xmlStrategy = new (await import("./XmlToolExecutionStrategy")).XmlToolExecutionStrategy()
		return xmlStrategy.executeToolCall(rooCodeTool, context)
	}

	private mapClaudeToolToRooCode(claudeToolCall: ClaudeNativeToolCall): ToolCall {
		// Map Claude native tools back to Roo Code format
		switch (claudeToolCall.name) {
			case "view":
				return {
					name: claudeToolCall.parameters.view_range ? "read_file" : "list_files",
					params: {
						path: claudeToolCall.parameters.path,
						...(claudeToolCall.parameters.view_range ? {
							start_line: claudeToolCall.parameters.view_range[0]?.toString(),
							end_line: claudeToolCall.parameters.view_range[1]?.toString()
						} : {})
					}
				}
			case "create":
				return {
					name: "write_to_file",
					params: {
						path: claudeToolCall.parameters.path,
						content: claudeToolCall.parameters.file_text
					}
				}
			case "str_replace":
				return {
					name: "apply_diff",
					params: {
						path: claudeToolCall.parameters.path,
						diff: this.createDiffFromStrReplace(
							claudeToolCall.parameters.old_str,
							claudeToolCall.parameters.new_str
						)
					}
				}
			case "insert":
				return {
					name: "insert_content",
					params: {
						path: claudeToolCall.parameters.path,
						line: claudeToolCall.parameters.insert_line.toString(),
						content: claudeToolCall.parameters.new_str
					}
				}
			default:
				return {
					name: claudeToolCall.name as ToolName,
					params: claudeToolCall.parameters
				}
		}
	}

	private createDiffFromStrReplace(oldStr: string, newStr: string): string {
		// Create a simple diff format from old and new strings
		const oldLines = oldStr.split('\n')
		const newLines = newStr.split('\n')

		let diff = "--- a/file\n+++ b/file\n@@ -1," + oldLines.length + " +1," + newLines.length + " @@\n"

		oldLines.forEach(line => {
			diff += "-" + line + "\n"
		})

		newLines.forEach(line => {
			diff += "+" + line + "\n"
		})

		return diff
	}
}

interface ClaudeNativeToolCall {
	name: string
	parameters: Record<string, any>
}
