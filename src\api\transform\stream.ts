export type ApiStream = AsyncGenerator<ApiStreamChunk>

export type ApiStreamChunk = ApiStreamTextChunk | ApiStreamUsageChunk | ApiStreamReasoningChunk | ApiStreamError | ApiStreamToolUseChunk | ApiStreamToolInputDeltaChunk

export interface ApiStreamError {
	type: "error"
	error: string
	message: string
}

export interface ApiStreamTextChunk {
	type: "text"
	text: string
}

export interface ApiStreamReasoningChunk {
	type: "reasoning"
	text: string
}

export interface ApiStreamUsageChunk {
	type: "usage"
	inputTokens: number
	outputTokens: number
	cacheWriteTokens?: number
	cacheReadTokens?: number
	reasoningTokens?: number
	totalCost?: number
}

export interface ApiStreamToolUseChunk {
	type: "tool_use"
	toolUse: {
		id: string
		name: string
		input: any
	}
}

export interface ApiStreamToolInputDeltaChunk {
	type: "tool_input_delta"
	delta: string
}
