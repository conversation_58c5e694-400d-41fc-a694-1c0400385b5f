import { 
	ToolExecutionStrategy, 
	ToolExecutionMode, 
	ToolName, 
	ToolCall, 
	ToolExecutionContext, 
	ToolResponse,
	ToolDefinition
} from "../../../shared/tools"

/**
 * XML-based tool execution strategy (current implementation)
 * This maintains backward compatibility with the existing XML tag approach
 */
export class XmlToolExecutionStrategy implements ToolExecutionStrategy {
	readonly mode: ToolExecutionMode = "xml"

	canExecute(toolName: ToolName, modelProvider: string, modelId: string): boolean {
		// XML strategy can work with any model
		return true
	}

	async executeToolCall(toolCall: ToolCall, context: ToolExecutionContext): Promise<ToolResponse> {
		// This delegates to the existing tool execution logic
		// The actual tool execution happens in presentAssistantMessage.ts
		// This is a wrapper to maintain the strategy pattern
		
		const { cline, askApproval, handleError, pushToolResult, removeClosingTag } = context
		
		// Convert ToolCall to ToolUse format expected by existing code
		const block = {
			type: "tool_use" as const,
			name: toolCall.name,
			params: toolCall.params,
			partial: false
		}

		// Execute using existing tool logic
		return this.executeExistingToolLogic(block, context)
	}

	formatToolsForPrompt(tools: ToolDefinition[]): string {
		// Return XML-formatted tool descriptions for the system prompt
		// This uses the existing tool description generation logic
		return tools.map(tool => this.formatToolAsXml(tool)).join("\n\n")
	}

	private formatToolAsXml(tool: ToolDefinition): string {
		// Format tool description in XML format for the prompt
		// This maintains the current XML tag approach
		const paramDescriptions = Object.entries(tool.parameters)
			.map(([name, desc]) => `- ${name}: ${desc}`)
			.join("\n")

		return `## ${tool.name}
${tool.description}

Parameters:
${paramDescriptions}

Usage:
<${tool.name}>
${Object.keys(tool.parameters).map(param => `<${param}>value</${param}>`).join("\n")}
</${tool.name}>`
	}

	private async executeExistingToolLogic(block: any, context: ToolExecutionContext): Promise<ToolResponse> {
		// Import and execute the existing tool logic
		// This is a temporary bridge until we fully migrate to the strategy pattern
		
		const { cline, askApproval, handleError, pushToolResult, removeClosingTag } = context

		// Execute the tool using the existing switch statement logic
		// from presentAssistantMessage.ts
		switch (block.name) {
			case "write_to_file":
				const { writeToFileTool } = await import("../writeToFileTool")
				await writeToFileTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "read_file":
				const { readFileTool } = await import("../readFileTool")
				await readFileTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "execute_command":
				const { executeCommandTool } = await import("../executeCommandTool")
				await executeCommandTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "apply_diff":
				const { applyDiffTool } = await import("../multiApplyDiffTool")
				await applyDiffTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "list_files":
				const { listFilesTool } = await import("../listFilesTool")
				await listFilesTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "search_files":
				const { searchFilesTool } = await import("../searchFilesTool")
				await searchFilesTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "insert_content":
				const { insertContentTool } = await import("../insertContentTool")
				await insertContentTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "search_and_replace":
				const { searchAndReplaceTool } = await import("../searchAndReplaceTool")
				await searchAndReplaceTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "list_code_definition_names":
				const { listCodeDefinitionNamesTool } = await import("../listCodeDefinitionNamesTool")
				await listCodeDefinitionNamesTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "browser_action":
				const { browserActionTool } = await import("../browserActionTool")
				await browserActionTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "use_mcp_tool":
				const { useMcpToolTool } = await import("../useMcpToolTool")
				await useMcpToolTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "access_mcp_resource":
				const { accessMcpResourceTool } = await import("../accessMcpResourceTool")
				await accessMcpResourceTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "ask_followup_question":
				const { askFollowupQuestionTool } = await import("../askFollowupQuestionTool")
				await askFollowupQuestionTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "attempt_completion":
				const { attemptCompletionTool } = await import("../attemptCompletionTool")
				// Note: attemptCompletionTool has additional parameters, this is simplified
				await attemptCompletionTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!, () => "", () => Promise.resolve(true))
				break
			case "switch_mode":
				const { switchModeTool } = await import("../switchModeTool")
				await switchModeTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "new_task":
				const { newTaskTool } = await import("../newTaskTool")
				await newTaskTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "fetch_instructions":
				const { fetchInstructionsTool } = await import("../fetchInstructionsTool")
				await fetchInstructionsTool(cline, block, askApproval, handleError, pushToolResult)
				break
			case "codebase_search":
				const { codebaseSearchTool } = await import("../codebaseSearchTool")
				await codebaseSearchTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			case "update_todo_list":
				const { updateTodoListTool } = await import("../updateTodoListTool")
				await updateTodoListTool(cline, block, askApproval, handleError, pushToolResult, removeClosingTag!)
				break
			default:
				throw new Error(`Unknown tool: ${block.name}`)
		}

		// Return a success response - the actual result is handled by pushToolResult
		return "Tool executed successfully via XML strategy"
	}
}
